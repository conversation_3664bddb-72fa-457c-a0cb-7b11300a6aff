{"version": 3, "sources": ["p__user__login__index-async.4792984285681303824.hot-update.js", "src/pages/user/login/index.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'p__user__login__index',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='11732318052140150111';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"common\",\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "/**\n * 登录页面\n * 实现双阶段认证的第一阶段：账号登录\n */\n\nimport { MailOutlined, SafetyOutlined } from '@ant-design/icons';\nimport { Helmet, history, useModel } from '@umijs/max';\nimport {\n  Button,\n  Card,\n  Form,\n  Input,\n  message,\n  Space,\n  Typography,\n} from 'antd';\nimport { createStyles } from 'antd-style';\nimport React, { useState, useCallback, useMemo, useEffect } from 'react';\nimport { Footer } from '@/components';\nimport { AuthService } from '@/services';\nimport type { LoginRequest, SendVerificationCodeRequest } from '@/types/api';\nimport Settings from '../../../../config/defaultSettings';\n\nconst { Title, Text } = Typography;\n\n// 登录表单组件（移到外部避免重新创建）\nconst LoginFormComponent: React.FC<{\n  form: any;\n  handleLogin: (values: LoginRequest) => void;\n  handleSendCode: () => void;\n  sendingCode: boolean;\n  countdown: number;\n  loading: boolean;\n}> = React.memo(({ form, handleLogin, handleSendCode, sendingCode, countdown, loading }) => {\n  // 使用 useMemo 稳定按钮渲染，避免因倒计时变化导致输入框重新渲染\n  const sendCodeButton = useMemo(() => (\n    <Button\n      type=\"link\"\n      size=\"small\"\n      disabled={countdown > 0 || sendingCode}\n      loading={sendingCode}\n      onClick={handleSendCode}\n      style={{ padding: 0, height: 'auto' }}\n    >\n      {countdown > 0 ? `${countdown}s后重发` : '发送验证码'}\n    </Button>\n  ), [countdown, sendingCode, handleSendCode]);\n\n  // 使用 useMemo 稳定邮箱输入框，避免重新渲染\n  const emailField = useMemo(() => (\n    <Form.Item\n      key=\"email-field\"\n      name=\"email\"\n      rules={[\n        { required: true, message: '请输入邮箱！' },\n        {\n          validator: (_, value) => {\n            if (!value || validateEmail(value)) {\n              return Promise.resolve();\n            }\n            return Promise.reject(new Error('请输入有效的邮箱地址！'));\n          },\n        },\n      ]}\n    >\n      <Input\n        key=\"email-input\"\n        prefix={<MailOutlined />}\n        placeholder=\"邮箱\"\n        autoComplete=\"email\"\n      />\n    </Form.Item>\n  ), []);\n\n  // 使用 useMemo 稳定验证码输入框，只在按钮变化时重新渲染\n  const codeField = useMemo(() => (\n    <Form.Item\n      key=\"code-field\"\n      name=\"code\"\n      rules={[\n        { required: true, message: '请输入验证码！' },\n        { len: 6, message: '验证码为6位数字！' },\n        { pattern: /^\\d{6}$/, message: '验证码只能包含数字！' },\n      ]}\n    >\n      <Input\n        key=\"code-input\"\n        prefix={<SafetyOutlined />}\n        placeholder=\"6位验证码\"\n        maxLength={6}\n        suffix={sendCodeButton}\n      />\n    </Form.Item>\n  ), [sendCodeButton]);\n\n  return (\n    <Form\n      form={form}\n      name=\"login\"\n      size=\"large\"\n      onFinish={handleLogin}\n      autoComplete=\"off\"\n    >\n      {emailField}\n      {codeField}\n\n      {/* 提示信息 */}\n      <div style={{ marginBottom: 16, textAlign: 'center' }}>\n        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n          新用户将自动完成注册并登录\n        </Text>\n      </div>\n\n      <Form.Item>\n        <Button type=\"primary\" htmlType=\"submit\" loading={loading} block>\n          登录 / 注册\n        </Button>\n      </Form.Item>\n    </Form>\n  );\n});\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    container: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh',\n      overflow: 'auto',\n      backgroundImage:\n        \"url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')\",\n      backgroundSize: '100% 100%',\n    },\n    content: {\n      flex: 1,\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: '32px 16px',\n    },\n    header: {\n      marginBottom: 40,\n      textAlign: 'center',\n    },\n    logo: {\n      marginBottom: 16,\n    },\n    title: {\n      marginBottom: 0,\n    },\n    loginCard: {\n      width: '100%',\n      maxWidth: 400,\n      boxShadow: token.boxShadowTertiary,\n    },\n    footer: {\n      marginTop: 40,\n      textAlign: 'center',\n    },\n    lang: {\n      width: 42,\n      height: 42,\n      lineHeight: '42px',\n      position: 'fixed',\n      right: 16,\n      top: 16,\n      borderRadius: token.borderRadius,\n      ':hover': {\n        backgroundColor: token.colorBgTextHover,\n      },\n    },\n  };\n});\n\nconst LoginPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [sendingCode, setSendingCode] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const [form] = Form.useForm(); // 将表单实例提升到父组件\n  const { styles } = useStyles();\n  const { setInitialState } = useModel('@@initialState');\n\n  // 自定义邮箱验证函数\n  const validateEmail = (email: string) => {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  };\n\n  // 组件挂载时清除倒计时状态，避免页面刷新后无法输入\n  useEffect(() => {\n    setCountdown(0);\n  }, []);\n\n  // 倒计时效果\n  React.useEffect(() => {\n    let timer: NodeJS.Timeout;\n    if (countdown > 0) {\n      timer = setTimeout(() => {\n        setCountdown(countdown - 1);\n      }, 1000);\n    }\n    return () => {\n      if (timer) clearTimeout(timer);\n    };\n  }, [countdown]);\n\n  // 发送验证码\n  const handleSendCode = useCallback(async (type: 'login' | 'register' = 'login') => {\n    // 从表单获取邮箱值\n    const email = form.getFieldValue('email');\n    console.log('发送验证码前的邮箱值:', email);\n\n    if (!email || !validateEmail(email)) {\n      message.error('请输入有效的邮箱地址');\n      return;\n    }\n\n    setSendingCode(true);\n    try {\n      const request: SendVerificationCodeRequest = { email, type };\n      const response = await AuthService.sendVerificationCode(request);\n\n      if (response.success) {\n        message.success(response.message);\n        setCountdown(60); // 60秒倒计时\n\n        // 验证码发送成功后检查表单值\n        console.log('发送验证码成功后的邮箱值:', form.getFieldValue('email'));\n\n        // 在开发环境中提示查看控制台\n        if (process.env.NODE_ENV === 'development') {\n          message.info('开发环境：请查看浏览器控制台或后端日志获取验证码', 5);\n        }\n      } else {\n        message.error(response.message);\n        if (response.nextSendTime) {\n          setCountdown(response.nextSendTime);\n        }\n      }\n    } catch (error) {\n      console.error('发送验证码失败:', error);\n      message.error('发送验证码失败，请稍后重试');\n    } finally {\n      setSendingCode(false);\n    }\n  }, [form]);\n\n  // 处理登录/注册\n  const handleLogin = useCallback(async (values: LoginRequest) => {\n    setLoading(true);\n    try {\n      const response = await AuthService.login(values);\n      message.success('登录成功！');\n\n      // 登录成功后，刷新 initialState\n      await setInitialState((prevState) => ({\n        ...prevState,\n        currentUser: response.user,\n        currentTeam: response.teams.length > 0 ? response.teams[0] : undefined,\n      }));\n\n      // 根据团队数量进行不同的跳转处理\n      if (response.teams.length === 0) {\n        // 没有团队，跳转到创建团队页面\n        history.push('/team/create');\n      } else {\n        // 有团队（无论一个还是多个），都跳转到个人中心整合页面\n        history.push('/personal-center', { teams: response.teams });\n      }\n    } catch (error) {\n      console.error('登录失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, [setInitialState]);\n\n  // 注册功能已移除，统一使用验证码登录/注册流程\n\n  return (\n    <div className={styles.container}>\n      <Helmet>\n        <title>\n          登录 / 注册\n          {Settings.title && ` - ${Settings.title}`}\n        </title>\n      </Helmet>\n      <div className={styles.content}>\n        <div className={styles.header}>\n          <Space direction=\"vertical\" align=\"center\" size=\"large\">\n            <div className={styles.logo}>\n              <img src=\"/logo.svg\" alt=\"TeamAuth\" height={48} />\n            </div>\n            <div className={styles.title}>\n              <Title level={2}>团队管理系统</Title>\n              <Text type=\"secondary\">现代化的团队协作与管理平台</Text>\n            </div>\n          </Space>\n        </div>\n\n        <Card className={styles.loginCard}>\n          <LoginFormComponent\n            form={form}\n            handleLogin={handleLogin}\n            handleSendCode={() => handleSendCode('login')}\n            sendingCode={sendingCode}\n            countdown={countdown}\n            loading={loading}\n          />\n        </Card>\n\n        <div className={styles.footer}>\n          <Text type=\"secondary\">© 2025 TeamAuth. All rights reserved.</Text>\n        </div>\n      </div>\n      <Footer />\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,yBACA;IACE,SAAS;;;;;;wCC6Tb;;;2BAAA;;;;;;;0CA3T6C;wCACH;yCASnC;8CACsB;oFACoC;+CAC1C;6CACK;6FAEP;;;;;;;;;;;YAErB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAElC,qBAAqB;YACrB,MAAM,mCAOD,cAAK,CAAC,IAAI,IAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE;;gBACrF,sCAAsC;gBACtC,MAAM,iBAAiB,IAAA,cAAO,EAAC,kBAC7B,2BAAC,YAAM;wBACL,MAAK;wBACL,MAAK;wBACL,UAAU,YAAY,KAAK;wBAC3B,SAAS;wBACT,SAAS;wBACT,OAAO;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;kCAEnC,YAAY,IAAI,CAAC,EAAE,UAAU,IAAI,CAAC,GAAG;;;;;8BAEvC;oBAAC;oBAAW;oBAAa;iBAAe;gBAE3C,4BAA4B;gBAC5B,MAAM,aAAa,IAAA,cAAO,EAAC,kBACzB,2BAAC,UAAI,CAAC,IAAI;wBAER,MAAK;wBACL,OAAO;4BACL;gCAAE,UAAU;gCAAM,SAAS;4BAAS;4BACpC;gCACE,WAAW,CAAC,GAAG;oCACb,IAAI,CAAC,SAAS,cAAc,QAC1B,OAAO,QAAQ,OAAO;oCAExB,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;gCAClC;4BACF;yBACD;kCAED,cAAA,2BAAC,WAAK;4BAEJ,sBAAQ,2BAAC,mBAAY;;;;;4BACrB,aAAY;4BACZ,cAAa;2BAHT;;;;;uBAfF;;;;8BAqBL,EAAE;gBAEL,kCAAkC;gBAClC,MAAM,YAAY,IAAA,cAAO,EAAC,kBACxB,2BAAC,UAAI,CAAC,IAAI;wBAER,MAAK;wBACL,OAAO;4BACL;gCAAE,UAAU;gCAAM,SAAS;4BAAU;4BACrC;gCAAE,KAAK;gCAAG,SAAS;4BAAY;4BAC/B;gCAAE,SAAS;gCAAW,SAAS;4BAAa;yBAC7C;kCAED,cAAA,2BAAC,WAAK;4BAEJ,sBAAQ,2BAAC,qBAAc;;;;;4BACvB,aAAY;4BACZ,WAAW;4BACX,QAAQ;2BAJJ;;;;;uBATF;;;;8BAgBL;oBAAC;iBAAe;gBAEnB,qBACE,2BAAC,UAAI;oBACH,MAAM;oBACN,MAAK;oBACL,MAAK;oBACL,UAAU;oBACV,cAAa;;wBAEZ;wBACA;sCAGD,2BAAC;4BAAI,OAAO;gCAAE,cAAc;gCAAI,WAAW;4BAAS;sCAClD,cAAA,2BAAC;gCAAK,MAAK;gCAAY,OAAO;oCAAE,UAAU;gCAAO;0CAAG;;;;;;;;;;;sCAKtD,2BAAC,UAAI,CAAC,IAAI;sCACR,cAAA,2BAAC,YAAM;gCAAC,MAAK;gCAAU,UAAS;gCAAS,SAAS;gCAAS,KAAK;0CAAC;;;;;;;;;;;;;;;;;YAMzE;iBA9FM;YAgGN,MAAM,YAAY,IAAA,uBAAY,EAAC,CAAC,EAAE,KAAK,EAAE;gBACvC,OAAO;oBACL,WAAW;wBACT,SAAS;wBACT,eAAe;wBACf,QAAQ;wBACR,UAAU;wBACV,iBACE;wBACF,gBAAgB;oBAClB;oBACA,SAAS;wBACP,MAAM;wBACN,SAAS;wBACT,eAAe;wBACf,gBAAgB;wBAChB,YAAY;wBACZ,SAAS;oBACX;oBACA,QAAQ;wBACN,cAAc;wBACd,WAAW;oBACb;oBACA,MAAM;wBACJ,cAAc;oBAChB;oBACA,OAAO;wBACL,cAAc;oBAChB;oBACA,WAAW;wBACT,OAAO;wBACP,UAAU;wBACV,WAAW,MAAM,iBAAiB;oBACpC;oBACA,QAAQ;wBACN,WAAW;wBACX,WAAW;oBACb;oBACA,MAAM;wBACJ,OAAO;wBACP,QAAQ;wBACR,YAAY;wBACZ,UAAU;wBACV,OAAO;wBACP,KAAK;wBACL,cAAc,MAAM,YAAY;wBAChC,UAAU;4BACR,iBAAiB,MAAM,gBAAgB;wBACzC;oBACF;gBACF;YACF;YAEA,MAAM,YAAsB;;gBAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAC;gBAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;gBAC3C,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO,IAAI,cAAc;gBAC7C,MAAM,EAAE,MAAM,EAAE,GAAG;gBACnB,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;gBAErC,YAAY;gBACZ,MAAM,iBAAgB,CAAC;oBACrB,MAAM,aAAa;oBACnB,OAAO,WAAW,IAAI,CAAC;gBACzB;gBAEA,2BAA2B;gBAC3B,IAAA,gBAAS,EAAC;oBACR,aAAa;gBACf,GAAG,EAAE;gBAEL,QAAQ;gBACR,cAAK,CAAC,SAAS,CAAC;oBACd,IAAI;oBACJ,IAAI,YAAY,GACd,QAAQ,WAAW;wBACjB,aAAa,YAAY;oBAC3B,GAAG;oBAEL,OAAO;wBACL,IAAI,OAAO,aAAa;oBAC1B;gBACF,GAAG;oBAAC;iBAAU;gBAEd,QAAQ;gBACR,MAAM,iBAAiB,IAAA,kBAAW,EAAC,OAAO,OAA6B,OAAO;oBAC5E,WAAW;oBACX,MAAM,QAAQ,KAAK,aAAa,CAAC;oBACjC,QAAQ,GAAG,CAAC,eAAe;oBAE3B,IAAI,CAAC,SAAS,CAAC,eAAc,QAAQ;wBACnC,aAAO,CAAC,KAAK,CAAC;wBACd;oBACF;oBAEA,eAAe;oBACf,IAAI;wBACF,MAAM,UAAuC;4BAAE;4BAAO;wBAAK;wBAC3D,MAAM,WAAW,MAAM,qBAAW,CAAC,oBAAoB,CAAC;wBAExD,IAAI,SAAS,OAAO,EAAE;4BACpB,aAAO,CAAC,OAAO,CAAC,SAAS,OAAO;4BAChC,aAAa,KAAK,SAAS;4BAE3B,gBAAgB;4BAChB,QAAQ,GAAG,CAAC,iBAAiB,KAAK,aAAa,CAAC;4BAI9C,aAAO,CAAC,IAAI,CAAC,4BAA4B;wBAE7C,OAAO;4BACL,aAAO,CAAC,KAAK,CAAC,SAAS,OAAO;4BAC9B,IAAI,SAAS,YAAY,EACvB,aAAa,SAAS,YAAY;wBAEtC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,YAAY;wBAC1B,aAAO,CAAC,KAAK,CAAC;oBAChB,SAAU;wBACR,eAAe;oBACjB;gBACF,GAAG;oBAAC;iBAAK;gBAET,UAAU;gBACV,MAAM,cAAc,IAAA,kBAAW,EAAC,OAAO;oBACrC,WAAW;oBACX,IAAI;wBACF,MAAM,WAAW,MAAM,qBAAW,CAAC,KAAK,CAAC;wBACzC,aAAO,CAAC,OAAO,CAAC;wBAEhB,wBAAwB;wBACxB,MAAM,gBAAgB,CAAC,YAAe,CAAA;gCACpC,GAAG,SAAS;gCACZ,aAAa,SAAS,IAAI;gCAC1B,aAAa,SAAS,KAAK,CAAC,MAAM,GAAG,IAAI,SAAS,KAAK,CAAC,EAAE,GAAG;4BAC/D,CAAA;wBAEA,kBAAkB;wBAClB,IAAI,SAAS,KAAK,CAAC,MAAM,KAAK,GAC5B,iBAAiB;wBACjB,YAAO,CAAC,IAAI,CAAC;6BAEb,6BAA6B;wBAC7B,YAAO,CAAC,IAAI,CAAC,oBAAoB;4BAAE,OAAO,SAAS,KAAK;wBAAC;oBAE7D,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,SAAS;oBACzB,SAAU;wBACR,WAAW;oBACb;gBACF,GAAG;oBAAC;iBAAgB;gBAEpB,yBAAyB;gBAEzB,qBACE,2BAAC;oBAAI,WAAW,OAAO,SAAS;;sCAC9B,2BAAC,WAAM;sCACL,cAAA,2BAAC;;oCAAM;oCAEJ,wBAAQ,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,wBAAQ,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;sCAG7C,2BAAC;4BAAI,WAAW,OAAO,OAAO;;8CAC5B,2BAAC;oCAAI,WAAW,OAAO,MAAM;8CAC3B,cAAA,2BAAC,WAAK;wCAAC,WAAU;wCAAW,OAAM;wCAAS,MAAK;;0DAC9C,2BAAC;gDAAI,WAAW,OAAO,IAAI;0DACzB,cAAA,2BAAC;oDAAI,KAAI;oDAAY,KAAI;oDAAW,QAAQ;;;;;;;;;;;0DAE9C,2BAAC;gDAAI,WAAW,OAAO,KAAK;;kEAC1B,2BAAC;wDAAM,OAAO;kEAAG;;;;;;kEACjB,2BAAC;wDAAK,MAAK;kEAAY;;;;;;;;;;;;;;;;;;;;;;;8CAK7B,2BAAC,UAAI;oCAAC,WAAW,OAAO,SAAS;8CAC/B,cAAA,2BAAC;wCACC,MAAM;wCACN,aAAa;wCACb,gBAAgB,IAAM,eAAe;wCACrC,aAAa;wCACb,WAAW;wCACX,SAAS;;;;;;;;;;;8CAIb,2BAAC;oCAAI,WAAW,OAAO,MAAM;8CAC3B,cAAA,2BAAC;wCAAK,MAAK;kDAAY;;;;;;;;;;;;;;;;;sCAG3B,2BAAC,kBAAM;;;;;;;;;;;YAGb;gBA/IM;;oBAIW,UAAI,CAAC;oBACD;oBACS,aAAQ;;;kBANhC;gBAiJN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ID7TD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,wCAAuC;YAAC;YAAS;SAAuC;IAAA;;AACx+B"}