package com.teammanage.performance;

import static org.junit.jupiter.api.Assertions.*;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import com.teammanage.dto.request.SendVerificationCodeRequest;
import com.teammanage.service.VerificationCodeService;

/**
 * 验证码服务性能测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
public class VerificationCodePerformanceTest {

    @Autowired
    private VerificationCodeService verificationCodeService;

    /**
     * 测试验证码生成性能
     */
    @Test
    public void testVerificationCodeGenerationPerformance() throws InterruptedException {
        int threadCount = 10;
        int requestsPerThread = 100;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < requestsPerThread; j++) {
                        try {
                            SendVerificationCodeRequest request = new SendVerificationCodeRequest();
                            request.setEmail("test" + threadId + "_" + j + "@example.com");
                            request.setType("login");
                            
                            verificationCodeService.sendVerificationCode(request);
                            successCount.incrementAndGet();
                            
                            // 模拟一些延迟
                            Thread.sleep(10);
                        } catch (Exception e) {
                            errorCount.incrementAndGet();
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("性能测试结果:");
        System.out.println("总请求数: " + (threadCount * requestsPerThread));
        System.out.println("成功数: " + successCount.get());
        System.out.println("失败数: " + errorCount.get());
        System.out.println("总耗时: " + duration + "ms");
        System.out.println("平均每秒处理: " + (successCount.get() * 1000.0 / duration) + " 请求");
        
        // 验证性能指标
        assertTrue(successCount.get() > 0, "应该有成功的请求");
        assertTrue(duration < 30000, "总耗时应该小于30秒");
        assertTrue((successCount.get() * 1000.0 / duration) > 10, "每秒应该能处理超过10个请求");
    }

    /**
     * 测试并发验证码验证
     */
    @Test
    public void testConcurrentVerificationCodeValidation() throws InterruptedException {
        // 首先生成一个验证码
        SendVerificationCodeRequest sendRequest = new SendVerificationCodeRequest();
        sendRequest.setEmail("<EMAIL>");
        sendRequest.setType("login");
        
        verificationCodeService.sendVerificationCode(sendRequest);
        
        // 模拟多个线程同时验证同一个验证码
        int threadCount = 5;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    // 注意：这里使用错误的验证码来测试并发安全性
                    verificationCodeService.verifyCode("<EMAIL>", "wrong_code", "login");
                    successCount.incrementAndGet();
                } catch (Exception e) {
                    errorCount.incrementAndGet();
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(10, TimeUnit.SECONDS);
        executor.shutdown();
        
        System.out.println("并发验证测试结果:");
        System.out.println("成功数: " + successCount.get());
        System.out.println("失败数: " + errorCount.get());
        
        // 所有验证都应该失败（因为使用了错误的验证码）
        assertEquals(0, successCount.get(), "使用错误验证码应该全部失败");
        assertEquals(threadCount, errorCount.get(), "所有线程都应该收到错误");
    }

    /**
     * 测试缓存性能
     */
    @Test
    public void testCachePerformance() throws InterruptedException {
        int operationCount = 1000;
        long startTime = System.currentTimeMillis();
        
        // 测试大量的验证码生成和查询
        for (int i = 0; i < operationCount; i++) {
            SendVerificationCodeRequest request = new SendVerificationCodeRequest();
            request.setEmail("cache_test_" + i + "@example.com");
            request.setType("login");
            
            verificationCodeService.sendVerificationCode(request);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("缓存性能测试结果:");
        System.out.println("操作数: " + operationCount);
        System.out.println("总耗时: " + duration + "ms");
        System.out.println("平均每操作耗时: " + (duration / (double) operationCount) + "ms");
        
        // 验证缓存性能
        assertTrue(duration < 10000, "1000次操作应该在10秒内完成");
        assertTrue((duration / (double) operationCount) < 10, "平均每次操作应该小于10ms");
    }

    /**
     * 测试内存使用情况
     */
    @Test
    public void testMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 生成大量验证码
        for (int i = 0; i < 1000; i++) {
            SendVerificationCodeRequest request = new SendVerificationCodeRequest();
            request.setEmail("memory_test_" + i + "@example.com");
            request.setType("login");
            
            verificationCodeService.sendVerificationCode(request);
        }
        
        // 强制垃圾回收
        System.gc();
        Thread.yield();
        
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryIncrease = finalMemory - initialMemory;
        
        System.out.println("内存使用测试结果:");
        System.out.println("初始内存: " + (initialMemory / 1024 / 1024) + "MB");
        System.out.println("最终内存: " + (finalMemory / 1024 / 1024) + "MB");
        System.out.println("内存增长: " + (memoryIncrease / 1024 / 1024) + "MB");
        
        // 验证内存使用合理
        assertTrue(memoryIncrease < 50 * 1024 * 1024, "内存增长应该小于50MB");
    }
}
