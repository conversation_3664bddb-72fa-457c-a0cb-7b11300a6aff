package com.teammanage.integration;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.util.Arrays;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.teammanage.dto.request.InviteMembersRequest;
import com.teammanage.dto.request.AcceptInvitationByLinkRequest;

/**
 * 邀请链接集成测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Transactional
public class InvitationLinkIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    private static final String TEST_EMAIL = "<EMAIL>";
    private static final String TEAM_TOKEN = "test_team_token"; // 需要有效的团队Token
    private static String invitationToken;

    /**
     * 测试发送邀请并生成链接
     */
    @Test
    @Order(1)
    public void testSendInvitationWithLink() throws Exception {
        InviteMembersRequest request = new InviteMembersRequest();
        request.setEmails(Arrays.asList(TEST_EMAIL));
        request.setMessage("欢迎加入我们的团队！");

        MvcResult result = mockMvc.perform(post("/invitations/send")
                .header("Authorization", "Bearer " + TEAM_TOKEN)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalCount").value(1))
                .andExpect(jsonPath("$.data.successCount").value(1))
                .andExpected(jsonPath("$.data.invitations[0].email").value(TEST_EMAIL))
                .andExpect(jsonPath("$.data.invitations[0].invitationLink").exists())
                .andReturn();

        // 提取邀请令牌用于后续测试
        String responseContent = result.getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(responseContent);
        String invitationLink = jsonNode.get("data").get("invitations").get(0).get("invitationLink").asText();
        
        // 从链接中提取令牌
        invitationToken = invitationLink.substring(invitationLink.lastIndexOf("/") + 1);
        assertNotNull(invitationToken);
    }

    /**
     * 测试现有用户通过邀请链接加入团队
     */
    @Test
    @Order(2)
    public void testExistingUserAcceptInvitationByLink() throws Exception {
        // 首先发送邀请
        testSendInvitationWithLink();

        // 现有用户接受邀请（不需要提供注册信息）
        mockMvc.perform(post("/invitations/accept-by-link/" + invitationToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}")) // 空的请求体
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.success").value(true))
                .andExpect(jsonPath("$.data.teamId").exists())
                .andExpect(jsonPath("$.data.teamName").exists())
                .andExpect(jsonPath("$.data.userId").exists())
                .andExpect(jsonPath("$.data.isNewUser").value(false))
                .andExpect(jsonPath("$.data.nextAction").value("请等待团队管理员激活您的账号"));
    }

    /**
     * 测试新用户通过邀请链接注册并加入团队
     */
    @Test
    @Order(3)
    public void testNewUserAcceptInvitationByLink() throws Exception {
        // 使用新的邮箱发送邀请
        String newUserEmail = "<EMAIL>";
        
        InviteMembersRequest request = new InviteMembersRequest();
        request.setEmails(Arrays.asList(newUserEmail));
        request.setMessage("欢迎加入我们的团队！");

        MvcResult result = mockMvc.perform(post("/invitations/send")
                .header("Authorization", "Bearer " + TEAM_TOKEN)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andReturn();

        // 提取新的邀请令牌
        String responseContent = result.getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(responseContent);
        String newInvitationLink = jsonNode.get("data").get("invitations").get(0).get("invitationLink").asText();
        String newInvitationToken = newInvitationLink.substring(newInvitationLink.lastIndexOf("/") + 1);

        // 新用户接受邀请并注册
        AcceptInvitationByLinkRequest acceptRequest = new AcceptInvitationByLinkRequest();
        acceptRequest.setName("New User");
        acceptRequest.setEmail(newUserEmail);
        acceptRequest.setPassword("password123");
        acceptRequest.setMessage("很高兴加入团队！");

        mockMvc.perform(post("/invitations/accept-by-link/" + newInvitationToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(acceptRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.success").value(true))
                .andExpect(jsonPath("$.data.teamId").exists())
                .andExpect(jsonPath("$.data.userId").exists())
                .andExpect(jsonPath("$.data.isNewUser").value(true))
                .andExpect(jsonPath("$.data.nextAction").value("请等待团队管理员激活您的账号"));
    }

    /**
     * 测试无效邀请令牌
     */
    @Test
    @Order(4)
    public void testInvalidInvitationToken() throws Exception {
        String invalidToken = "invalid_token_123";

        mockMvc.perform(post("/invitations/accept-by-link/" + invalidToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.success").value(false))
                .andExpect(jsonPath("$.data.errorMessage").value("无效的邀请链接"));
    }

    /**
     * 测试新用户缺少注册信息的场景
     */
    @Test
    @Order(5)
    public void testNewUserMissingRegistrationInfo() throws Exception {
        // 使用另一个新邮箱
        String anotherNewEmail = "<EMAIL>";
        
        // 发送邀请...
        InviteMembersRequest request = new InviteMembersRequest();
        request.setEmails(Arrays.asList(anotherNewEmail));

        MvcResult result = mockMvc.perform(post("/invitations/send")
                .header("Authorization", "Bearer " + TEAM_TOKEN)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andReturn();

        // 提取邀请令牌
        String responseContent = result.getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(responseContent);
        String invitationLink = jsonNode.get("data").get("invitations").get(0).get("invitationLink").asText();
        String token = invitationLink.substring(invitationLink.lastIndexOf("/") + 1);

        // 新用户尝试接受邀请但缺少注册信息
        AcceptInvitationByLinkRequest incompleteRequest = new AcceptInvitationByLinkRequest();
        incompleteRequest.setName("Incomplete User");
        // 缺少密码

        mockMvc.perform(post("/invitations/accept-by-link/" + token)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(incompleteRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.success").value(false))
                .andExpect(jsonPath("$.data.errorMessage").value("新用户注册需要提供姓名和密码"));
    }
}
