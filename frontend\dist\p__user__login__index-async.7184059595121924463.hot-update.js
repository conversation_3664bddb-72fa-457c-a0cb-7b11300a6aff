globalThis.makoModuleHotUpdate('p__user__login__index', {
    modules: {
        "src/pages/user/login/index.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _antdstyle = __mako_require__("node_modules/antd-style/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _components = __mako_require__("src/components/index.ts");
            var _services = __mako_require__("src/services/index.ts");
            var _defaultSettings = /*#__PURE__*/ _interop_require_default._(__mako_require__("config/defaultSettings.ts"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            const useStyles = (0, _antdstyle.createStyles)(({ token })=>{
                return {
                    container: {
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100vh',
                        overflow: 'auto',
                        backgroundImage: "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
                        backgroundSize: '100% 100%'
                    },
                    content: {
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        padding: '32px 16px'
                    },
                    header: {
                        marginBottom: 40,
                        textAlign: 'center'
                    },
                    logo: {
                        marginBottom: 16
                    },
                    title: {
                        marginBottom: 0
                    },
                    loginCard: {
                        width: '100%',
                        maxWidth: 400,
                        boxShadow: token.boxShadowTertiary
                    },
                    footer: {
                        marginTop: 40,
                        textAlign: 'center'
                    },
                    lang: {
                        width: 42,
                        height: 42,
                        lineHeight: '42px',
                        position: 'fixed',
                        right: 16,
                        top: 16,
                        borderRadius: token.borderRadius,
                        ':hover': {
                            backgroundColor: token.colorBgTextHover
                        }
                    }
                };
            });
            const LoginPage = ()=>{
                _s();
                var _s1 = $RefreshSig$();
                const [loading, setLoading] = (0, _react.useState)(false);
                const [sendingCode, setSendingCode] = (0, _react.useState)(false);
                const [countdown, setCountdown] = (0, _react.useState)(0);
                const [form] = _antd.Form.useForm(); // 将表单实例提升到父组件
                const { styles } = useStyles();
                const { setInitialState } = (0, _max.useModel)('@@initialState');
                // 自定义邮箱验证函数
                const validateEmail = (email)=>{
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    return emailRegex.test(email);
                };
                // 倒计时效果
                _react.default.useEffect(()=>{
                    let timer;
                    if (countdown > 0) timer = setTimeout(()=>{
                        setCountdown(countdown - 1);
                    }, 1000);
                    return ()=>{
                        if (timer) clearTimeout(timer);
                    };
                }, [
                    countdown
                ]);
                // 发送验证码
                const handleSendCode = async (email, type)=>{
                    if (!email || !validateEmail(email)) {
                        _antd.message.error('请输入有效的邮箱地址');
                        return;
                    }
                    setSendingCode(true);
                    try {
                        const request = {
                            email,
                            type
                        };
                        const response = await _services.AuthService.sendVerificationCode(request);
                        if (response.success) {
                            _antd.message.success(response.message);
                            setCountdown(60); // 60秒倒计时
                            _antd.message.info('开发环境：请查看浏览器控制台或后端日志获取验证码', 5);
                        } else {
                            _antd.message.error(response.message);
                            if (response.nextSendTime) setCountdown(response.nextSendTime);
                        }
                    } catch (error) {
                        console.error('发送验证码失败:', error);
                        _antd.message.error('发送验证码失败，请稍后重试');
                    } finally{
                        setSendingCode(false);
                    }
                };
                // 处理登录/注册
                const handleLogin = async (values)=>{
                    setLoading(true);
                    try {
                        const response = await _services.AuthService.login(values);
                        _antd.message.success('登录成功！');
                        // 登录成功后，刷新 initialState
                        await setInitialState((prevState)=>({
                                ...prevState,
                                currentUser: response.user,
                                currentTeam: response.teams.length > 0 ? response.teams[0] : undefined
                            }));
                        // 根据团队数量进行不同的跳转处理
                        if (response.teams.length === 0) // 没有团队，跳转到创建团队页面
                        _max.history.push('/team/create');
                        else // 有团队（无论一个还是多个），都跳转到个人中心整合页面
                        _max.history.push('/personal-center', {
                            teams: response.teams
                        });
                    } catch (error) {
                        console.error('登录失败:', error);
                    } finally{
                        setLoading(false);
                    }
                };
                // 注册功能已移除，统一使用验证码登录/注册流程
                // 登录表单
                const LoginForm = ()=>{
                    _s1();
                    const [form] = _antd.Form.useForm();
                    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                        form: form,
                        name: "login",
                        size: "large",
                        onFinish: handleLogin,
                        autoComplete: "off",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                name: "email",
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入邮箱！'
                                    },
                                    {
                                        validator: (_, value)=>{
                                            if (!value || validateEmail(value)) return Promise.resolve();
                                            return Promise.reject(new Error('请输入有效的邮箱地址！'));
                                        }
                                    }
                                ],
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                    prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 192,
                                        columnNumber: 21
                                    }, void 0),
                                    placeholder: "邮箱",
                                    autoComplete: "email"
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 191,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 177,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                name: "code",
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入验证码！'
                                    },
                                    {
                                        len: 6,
                                        message: '验证码为6位数字！'
                                    },
                                    {
                                        pattern: /^\d{6}$/,
                                        message: '验证码只能包含数字！'
                                    }
                                ],
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                    prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SafetyOutlined, {}, void 0, false, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 207,
                                        columnNumber: 21
                                    }, void 0),
                                    placeholder: "6位验证码",
                                    maxLength: 6,
                                    suffix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "link",
                                        size: "small",
                                        disabled: countdown > 0 || sendingCode,
                                        loading: sendingCode,
                                        onClick: ()=>{
                                            const email = form.getFieldValue('email');
                                            handleSendCode(email, 'login');
                                        },
                                        style: {
                                            padding: 0,
                                            height: 'auto'
                                        },
                                        children: countdown > 0 ? `${countdown}s后重发` : '发送验证码'
                                    }, void 0, false, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 211,
                                        columnNumber: 15
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 206,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 198,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginBottom: 16,
                                    textAlign: 'center'
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    style: {
                                        fontSize: '12px'
                                    },
                                    children: "新用户将自动完成注册并登录"
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 230,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 229,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    htmlType: "submit",
                                    loading: loading,
                                    block: true,
                                    children: "登录 / 注册"
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 236,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 235,
                                columnNumber: 9
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 176,
                        columnNumber: 7
                    }, this);
                };
                _s1(LoginForm, "rI7DrJIrFu7YmlGWYiMFTzs8jF0=", false, function() {
                    return [
                        _antd.Form.useForm
                    ];
                });
                // 注册表单已移除，统一使用验证码登录/注册流程
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    className: styles.container,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.Helmet, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("title", {
                                children: [
                                    "登录 / 注册",
                                    _defaultSettings.default.title && ` - ${_defaultSettings.default.title}`
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 249,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 248,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            className: styles.content,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.header,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        direction: "vertical",
                                        align: "center",
                                        size: "large",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                className: styles.logo,
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("img", {
                                                    src: "/logo.svg",
                                                    alt: "TeamAuth",
                                                    height: 48
                                                }, void 0, false, {
                                                    fileName: "src/pages/user/login/index.tsx",
                                                    lineNumber: 258,
                                                    columnNumber: 15
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/user/login/index.tsx",
                                                lineNumber: 257,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                className: styles.title,
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                        level: 2,
                                                        children: "团队管理系统"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/user/login/index.tsx",
                                                        lineNumber: 261,
                                                        columnNumber: 15
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        type: "secondary",
                                                        children: "现代化的团队协作与管理平台"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/user/login/index.tsx",
                                                        lineNumber: 262,
                                                        columnNumber: 15
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/user/login/index.tsx",
                                                lineNumber: 260,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 256,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 255,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                    className: styles.loginCard,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(LoginForm, {}, void 0, false, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 268,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 267,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.footer,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "© 2025 TeamAuth. All rights reserved."
                                    }, void 0, false, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 272,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 271,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 254,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 275,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 247,
                    columnNumber: 5
                }, this);
            };
            _s(LoginPage, "24y3K/jg66+qZcnXO7ZCdLivE9o=", false, function() {
                return [
                    _antd.Form.useForm,
                    useStyles,
                    _max.useModel
                ];
            });
            _c = LoginPage;
            var _default = LoginPage;
            var _c;
            $RefreshReg$(_c, "LoginPage");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '16717928172183459131';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/pages/user/team-select/index.tsx": [
            "common",
            "src/pages/user/team-select/index.tsx"
        ]
    });
    ;
});

//# sourceMappingURL=p__user__login__index-async.7184059595121924463.hot-update.js.map