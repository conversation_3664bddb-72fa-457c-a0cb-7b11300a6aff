package com.teammanage.transaction;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.teammanage.dto.request.AcceptInvitationByLinkRequest;
import com.teammanage.entity.Account;
import com.teammanage.entity.TeamInvitation;
import com.teammanage.entity.TeamMember;
import com.teammanage.mapper.AccountMapper;
import com.teammanage.mapper.TeamInvitationMapper;
import com.teammanage.mapper.TeamMemberMapper;
import com.teammanage.service.TeamInvitationService;

/**
 * 事务一致性测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TransactionConsistencyTest {

    @Autowired
    private TeamInvitationService teamInvitationService;

    @SpyBean
    private AccountMapper accountMapper;

    @SpyBean
    private TeamInvitationMapper teamInvitationMapper;

    @SpyBean
    private TeamMemberMapper teamMemberMapper;

    /**
     * 测试邀请链接处理的事务一致性
     * 当用户创建失败时，邀请状态不应该被更新
     */
    @Test
    public void testInvitationProcessingTransactionConsistency() {
        // 模拟邀请令牌
        String invitationToken = "test_token";
        
        // 模拟邀请记录
        TeamInvitation mockInvitation = new TeamInvitation();
        mockInvitation.setId(1L);
        mockInvitation.setTeamId(1L);
        mockInvitation.setInviteeEmail("<EMAIL>");
        mockInvitation.setStatus(TeamInvitation.InvitationStatus.PENDING);
        
        // 模拟新用户注册请求
        AcceptInvitationByLinkRequest request = new AcceptInvitationByLinkRequest();
        request.setName("New User");
        request.setEmail("<EMAIL>");
        request.setPassword("password123");

        // 设置mock行为
        when(teamInvitationMapper.selectById(anyLong())).thenReturn(mockInvitation);
        when(accountMapper.findByEmail(anyString())).thenReturn(null); // 用户不存在
        
        // 模拟用户创建失败
        doThrow(new RuntimeException("Database error")).when(accountMapper).insert(any(Account.class));

        // 执行测试
        try {
            teamInvitationService.processInvitationByToken(invitationToken, request);
            fail("应该抛出异常");
        } catch (Exception e) {
            // 验证事务回滚
            // 邀请状态不应该被更新
            verify(teamInvitationMapper, never()).updateById(any(TeamInvitation.class));
            // 团队成员不应该被添加
            verify(teamMemberMapper, never()).insert(any(TeamMember.class));
        }
    }

    /**
     * 测试邀请创建的事务一致性
     * 当部分邀请创建失败时，应该回滚所有操作
     */
    @Test
    public void testInvitationCreationTransactionConsistency() {
        Long teamId = 1L;
        Long inviterId = 1L;
        String message = "Welcome to the team!";
        
        // 模拟邮箱列表
        java.util.List<String> emails = Arrays.asList(
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        );

        // 模拟第三个邀请插入失败
        doNothing().when(teamInvitationMapper).insert(any(TeamInvitation.class));
        doThrow(new RuntimeException("Database constraint violation"))
            .when(teamInvitationMapper).insert(argThat(invitation -> 
                "<EMAIL>".equals(invitation.getInviteeEmail())));

        // 执行测试
        try {
            teamInvitationService.createInvitations(teamId, inviterId, emails, message);
            fail("应该抛出异常");
        } catch (Exception e) {
            // 验证事务回滚
            // 由于事务回滚，前面成功的插入也应该被撤销
            assertTrue(e.getMessage().contains("Database constraint violation"));
        }
    }

    /**
     * 测试响应邀请的事务一致性
     * 当团队成员添加失败时，邀请状态不应该被更新
     */
    @Test
    public void testRespondInvitationTransactionConsistency() {
        Long invitationId = 1L;
        Long userId = 1L;
        boolean accept = true;
        String responseMessage = "Happy to join!";

        // 模拟邀请记录
        TeamInvitation mockInvitation = new TeamInvitation();
        mockInvitation.setId(invitationId);
        mockInvitation.setTeamId(1L);
        mockInvitation.setInviteeId(userId);
        mockInvitation.setStatus(TeamInvitation.InvitationStatus.PENDING);

        // 设置mock行为
        when(teamInvitationMapper.selectById(invitationId)).thenReturn(mockInvitation);
        
        // 模拟团队成员添加失败
        doThrow(new RuntimeException("Team member constraint violation"))
            .when(teamMemberMapper).insert(any(TeamMember.class));

        // 执行测试
        try {
            teamInvitationService.respondToInvitation(invitationId, userId, accept, responseMessage);
            fail("应该抛出异常");
        } catch (Exception e) {
            // 验证事务回滚
            // 邀请状态不应该被更新为ACCEPTED
            verify(teamInvitationMapper, never()).updateById(argThat(invitation -> 
                TeamInvitation.InvitationStatus.ACCEPTED.equals(invitation.getStatus())));
        }
    }

    /**
     * 测试并发邀请处理的一致性
     */
    @Test
    public void testConcurrentInvitationProcessing() throws InterruptedException {
        // 这个测试需要实际的数据库环境来验证
        // 模拟两个线程同时处理同一个邀请
        
        String invitationToken = "concurrent_test_token";
        
        // 模拟邀请记录
        TeamInvitation mockInvitation = new TeamInvitation();
        mockInvitation.setId(1L);
        mockInvitation.setTeamId(1L);
        mockInvitation.setInviteeEmail("<EMAIL>");
        mockInvitation.setStatus(TeamInvitation.InvitationStatus.PENDING);

        when(teamInvitationMapper.selectById(anyLong())).thenReturn(mockInvitation);
        when(accountMapper.findByEmail(anyString())).thenReturn(null);

        AcceptInvitationByLinkRequest request = new AcceptInvitationByLinkRequest();
        request.setName("Concurrent User");
        request.setEmail("<EMAIL>");
        request.setPassword("password123");

        // 在实际测试中，这里应该启动多个线程同时处理
        // 验证只有一个线程能成功处理邀请
        
        // 由于这是单元测试环境，我们主要验证方法的幂等性
        assertDoesNotThrow(() -> {
            teamInvitationService.processInvitationByToken(invitationToken, request);
        });
    }

    /**
     * 测试数据完整性约束
     */
    @Test
    public void testDataIntegrityConstraints() {
        // 测试必填字段验证
        AcceptInvitationByLinkRequest incompleteRequest = new AcceptInvitationByLinkRequest();
        incompleteRequest.setName("Incomplete User");
        // 缺少密码

        String invitationToken = "integrity_test_token";
        
        TeamInvitation mockInvitation = new TeamInvitation();
        mockInvitation.setId(1L);
        mockInvitation.setTeamId(1L);
        mockInvitation.setInviteeEmail("<EMAIL>");
        mockInvitation.setStatus(TeamInvitation.InvitationStatus.PENDING);

        when(teamInvitationMapper.selectById(anyLong())).thenReturn(mockInvitation);
        when(accountMapper.findByEmail(anyString())).thenReturn(null);

        // 执行测试
        var response = teamInvitationService.processInvitationByToken(invitationToken, incompleteRequest);
        
        // 验证业务逻辑验证
        assertFalse(response.getSuccess());
        assertTrue(response.getErrorMessage().contains("新用户注册需要提供姓名和密码"));
        
        // 验证没有进行数据库操作
        verify(accountMapper, never()).insert(any(Account.class));
        verify(teamMemberMapper, never()).insert(any(TeamMember.class));
    }
}
