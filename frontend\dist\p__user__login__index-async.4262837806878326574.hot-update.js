globalThis.makoModuleHotUpdate('p__user__login__index', {
    modules: {
        "src/pages/user/login/index.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _antdstyle = __mako_require__("node_modules/antd-style/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _components = __mako_require__("src/components/index.ts");
            var _services = __mako_require__("src/services/index.ts");
            var _defaultSettings = /*#__PURE__*/ _interop_require_default._(__mako_require__("config/defaultSettings.ts"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            var _s1 = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            // 邮箱验证函数（移到组件外部，避免作用域问题）
            const validateEmail = (email)=>{
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            };
            // 登录表单组件（移到外部避免重新创建）
            const LoginFormComponent = /*#__PURE__*/ _react.default.memo(_s(({ form, handleLogin, handleSendCode, sendingCode, countdown, loading })=>{
                _s();
                // 使用 useMemo 稳定按钮渲染，避免因倒计时变化导致输入框重新渲染
                const sendCodeButton = (0, _react.useMemo)(()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "link",
                        size: "small",
                        disabled: countdown > 0 || sendingCode,
                        loading: sendingCode,
                        onClick: handleSendCode,
                        style: {
                            padding: 0,
                            height: 'auto'
                        },
                        children: countdown > 0 ? `${countdown}s后重发` : '发送验证码'
                    }, void 0, false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 43,
                        columnNumber: 5
                    }, this), [
                    countdown,
                    sendingCode,
                    handleSendCode
                ]);
                // 使用 useMemo 稳定邮箱输入框，避免重新渲染
                const emailField = (0, _react.useMemo)(()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                        name: "email",
                        rules: [
                            {
                                required: true,
                                message: '请输入邮箱！'
                            },
                            {
                                validator: (_, value)=>{
                                    if (!value || validateEmail(value)) return Promise.resolve();
                                    return Promise.reject(new Error('请输入有效的邮箱地址！'));
                                }
                            }
                        ],
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 74,
                                columnNumber: 17
                            }, void 0),
                            placeholder: "邮箱",
                            autoComplete: "email"
                        }, "email-input", false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 72,
                            columnNumber: 7
                        }, this)
                    }, "email-field", false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 57,
                        columnNumber: 5
                    }, this), []);
                // 使用 useMemo 稳定验证码输入框，只在按钮变化时重新渲染
                const codeField = (0, _react.useMemo)(()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                        name: "code",
                        rules: [
                            {
                                required: true,
                                message: '请输入验证码！'
                            },
                            {
                                len: 6,
                                message: '验证码为6位数字！'
                            },
                            {
                                pattern: /^\d{6}$/,
                                message: '验证码只能包含数字！'
                            }
                        ],
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SafetyOutlined, {}, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 94,
                                columnNumber: 17
                            }, void 0),
                            placeholder: "6位验证码",
                            maxLength: 6,
                            suffix: sendCodeButton
                        }, "code-input", false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 92,
                            columnNumber: 7
                        }, this)
                    }, "code-field", false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 83,
                        columnNumber: 5
                    }, this), [
                    sendCodeButton
                ]);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: form,
                    name: "login",
                    size: "large",
                    onFinish: handleLogin,
                    autoComplete: "off",
                    children: [
                        emailField,
                        codeField,
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 16,
                                textAlign: 'center'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                style: {
                                    fontSize: '12px'
                                },
                                children: "新用户将自动完成注册并登录"
                            }, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 115,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 114,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "primary",
                                htmlType: "submit",
                                loading: loading,
                                block: true,
                                children: "登录 / 注册"
                            }, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 121,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 120,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 103,
                    columnNumber: 5
                }, this);
            }, "vH8AbKPV2dOQkAxU7xEfdsmzClM="));
            _c = LoginFormComponent;
            const useStyles = (0, _antdstyle.createStyles)(({ token })=>{
                return {
                    container: {
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100vh',
                        overflow: 'auto',
                        backgroundImage: "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
                        backgroundSize: '100% 100%'
                    },
                    content: {
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        padding: '32px 16px'
                    },
                    header: {
                        marginBottom: 40,
                        textAlign: 'center'
                    },
                    logo: {
                        marginBottom: 16
                    },
                    title: {
                        marginBottom: 0
                    },
                    loginCard: {
                        width: '100%',
                        maxWidth: 400,
                        boxShadow: token.boxShadowTertiary
                    },
                    footer: {
                        marginTop: 40,
                        textAlign: 'center'
                    },
                    lang: {
                        width: 42,
                        height: 42,
                        lineHeight: '42px',
                        position: 'fixed',
                        right: 16,
                        top: 16,
                        borderRadius: token.borderRadius,
                        ':hover': {
                            backgroundColor: token.colorBgTextHover
                        }
                    }
                };
            });
            const LoginPage = ()=>{
                _s1();
                const [loading, setLoading] = (0, _react.useState)(false);
                const [sendingCode, setSendingCode] = (0, _react.useState)(false);
                const [countdown, setCountdown] = (0, _react.useState)(0);
                const [form] = _antd.Form.useForm(); // 将表单实例提升到父组件
                const { styles } = useStyles();
                const { setInitialState } = (0, _max.useModel)('@@initialState');
                // 自定义邮箱验证函数
                const validateEmail = (email)=>{
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    return emailRegex.test(email);
                };
                // 组件挂载时清除倒计时状态，避免页面刷新后无法输入
                (0, _react.useEffect)(()=>{
                    setCountdown(0);
                }, []);
                // 倒计时效果
                _react.default.useEffect(()=>{
                    let timer;
                    if (countdown > 0) timer = setTimeout(()=>{
                        setCountdown(countdown - 1);
                    }, 1000);
                    return ()=>{
                        if (timer) clearTimeout(timer);
                    };
                }, [
                    countdown
                ]);
                // 发送验证码
                const handleSendCode = (0, _react.useCallback)(async (type = 'login')=>{
                    // 从表单获取邮箱值
                    const email = form.getFieldValue('email');
                    console.log('发送验证码前的邮箱值:', email);
                    if (!email || !validateEmail(email)) {
                        _antd.message.error('请输入有效的邮箱地址');
                        return;
                    }
                    setSendingCode(true);
                    try {
                        const request = {
                            email,
                            type
                        };
                        const response = await _services.AuthService.sendVerificationCode(request);
                        if (response.success) {
                            _antd.message.success(response.message);
                            setCountdown(60); // 60秒倒计时
                            // 验证码发送成功后检查表单值
                            console.log('发送验证码成功后的邮箱值:', form.getFieldValue('email'));
                            _antd.message.info('开发环境：请查看浏览器控制台或后端日志获取验证码', 5);
                        } else {
                            _antd.message.error(response.message);
                            if (response.nextSendTime) setCountdown(response.nextSendTime);
                        }
                    } catch (error) {
                        console.error('发送验证码失败:', error);
                        _antd.message.error('发送验证码失败，请稍后重试');
                    } finally{
                        setSendingCode(false);
                    }
                }, [
                    form
                ]);
                // 处理登录/注册
                const handleLogin = (0, _react.useCallback)(async (values)=>{
                    setLoading(true);
                    try {
                        const response = await _services.AuthService.login(values);
                        _antd.message.success('登录成功！');
                        // 登录成功后停止倒计时
                        setCountdown(0);
                        // 登录成功后，刷新 initialState
                        await setInitialState((prevState)=>({
                                ...prevState,
                                currentUser: response.user,
                                currentTeam: response.teams.length > 0 ? response.teams[0] : undefined
                            }));
                        // 根据团队数量进行不同的跳转处理
                        if (response.teams.length === 0) // 没有团队，跳转到创建团队页面
                        _max.history.push('/team/create');
                        else // 有团队（无论一个还是多个），都跳转到个人中心整合页面
                        _max.history.push('/personal-center', {
                            teams: response.teams
                        });
                    } catch (error) {
                        console.error('登录失败:', error);
                    } finally{
                        setLoading(false);
                    }
                }, [
                    setInitialState
                ]);
                // 注册功能已移除，统一使用验证码登录/注册流程
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    className: styles.container,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.Helmet, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("title", {
                                children: [
                                    "登录 / 注册",
                                    _defaultSettings.default.title && ` - ${_defaultSettings.default.title}`
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 292,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 291,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            className: styles.content,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.header,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        direction: "vertical",
                                        align: "center",
                                        size: "large",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                className: styles.logo,
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("img", {
                                                    src: "/logo.svg",
                                                    alt: "TeamAuth",
                                                    height: 48
                                                }, void 0, false, {
                                                    fileName: "src/pages/user/login/index.tsx",
                                                    lineNumber: 301,
                                                    columnNumber: 15
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/user/login/index.tsx",
                                                lineNumber: 300,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                className: styles.title,
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                        level: 2,
                                                        children: "团队管理系统"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/user/login/index.tsx",
                                                        lineNumber: 304,
                                                        columnNumber: 15
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        type: "secondary",
                                                        children: "现代化的团队协作与管理平台"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/user/login/index.tsx",
                                                        lineNumber: 305,
                                                        columnNumber: 15
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/user/login/index.tsx",
                                                lineNumber: 303,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 299,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 298,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                    className: styles.loginCard,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(LoginFormComponent, {
                                        form: form,
                                        handleLogin: handleLogin,
                                        handleSendCode: ()=>handleSendCode('login'),
                                        sendingCode: sendingCode,
                                        countdown: countdown,
                                        loading: loading
                                    }, void 0, false, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 311,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 310,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.footer,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "© 2025 TeamAuth. All rights reserved."
                                    }, void 0, false, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 322,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 321,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 297,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 325,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 290,
                    columnNumber: 5
                }, this);
            };
            _s1(LoginPage, "Yq4Fb8fQ18048Y8KY3QhqRjuF9Y=", false, function() {
                return [
                    _antd.Form.useForm,
                    useStyles,
                    _max.useModel
                ];
            });
            _c1 = LoginPage;
            var _default = LoginPage;
            var _c;
            var _c1;
            $RefreshReg$(_c, "LoginFormComponent");
            $RefreshReg$(_c1, "LoginPage");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '15412121967319189276';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/pages/user/team-select/index.tsx": [
            "common",
            "src/pages/user/team-select/index.tsx"
        ]
    });
    ;
});

//# sourceMappingURL=p__user__login__index-async.4262837806878326574.hot-update.js.map